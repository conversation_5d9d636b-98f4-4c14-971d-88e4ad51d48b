const fs = require('fs').promises;
const path = require('path');
const { v4: uuidv4 } = require('uuid');

/**
 * Đọc file text và trả về mảng các dòng (loại bỏ dòng trống và comment)
 * @param {string} filePath - Đường dẫn đến file
 * @returns {Promise<string[]>} - Mảng các dòng
 */
async function readTextFile(filePath) {
  try {
    const content = await fs.readFile(filePath, 'utf-8');
    return content
      .split('\n')
      .map(line => line.trim())
      .filter(line => line && !line.startsWith('#'));
  } catch (error) {
    console.error(`Error reading file ${filePath}:`, error);
    return [];
  }
}

/**
 * Đọc file accounts.txt và parse thành mảng objects
 * @param {string} filePath - Đường dẫn đến file accounts.txt
 * @returns {Promise<Array>} - Mảng các account objects
 */
async function readAccountsFile(filePath) {
  const lines = await readTextFile(filePath);
  return lines.map(line => {
    const [username, password] = line.split(':');
    return {
      id: uuidv4(),
      username: username?.trim(),
      password: password?.trim(),
      status: 'not_logged_in',
      proxy: null,
      stats: {
        followsToday: 0,
        followsThisSession: 0,
        lastActivity: null
      }
    };
  }).filter(account => account.username && account.password);
}

/**
 * Đọc file proxies.txt và parse thành mảng objects
 * @param {string} filePath - Đường dẫn đến file proxies.txt
 * @returns {Promise<Array>} - Mảng các proxy objects
 */
async function readProxiesFile(filePath) {
  const lines = await readTextFile(filePath);
  return lines.map(line => {
    const parts = line.split(':');
    if (parts.length >= 2) {
      return {
        id: uuidv4(),
        host: parts[0]?.trim(),
        port: parseInt(parts[1]?.trim()),
        username: parts[2]?.trim() || null,
        password: parts[3]?.trim() || null,
        isActive: true
      };
    }
    return null;
  }).filter(proxy => proxy && proxy.host && proxy.port);
}

/**
 * Đọc file comments.txt
 * @param {string} filePath - Đường dẫn đến file comments.txt
 * @returns {Promise<string[]>} - Mảng các comment
 */
async function readCommentsFile(filePath) {
  return await readTextFile(filePath);
}

/**
 * Tạo delay ngẫu nhiên
 * @param {number} min - Thời gian tối thiểu (ms)
 * @param {number} max - Thời gian tối đa (ms)
 * @returns {Promise<void>}
 */
function sleep(min, max = null) {
  const delay = max ? Math.floor(Math.random() * (max - min + 1)) + min : min;
  return new Promise(resolve => setTimeout(resolve, delay));
}

/**
 * Tạo UUID mới
 * @returns {string} - UUID string
 */
function generateUUID() {
  return uuidv4();
}

/**
 * Lấy comment ngẫu nhiên từ danh sách
 * @param {string[]} comments - Danh sách comments
 * @returns {string} - Comment ngẫu nhiên
 */
function getRandomComment(comments) {
  if (!comments || comments.length === 0) {
    return 'Nice video!';
  }
  return comments[Math.floor(Math.random() * comments.length)];
}

/**
 * Tạo số ngẫu nhiên trong khoảng
 * @param {number} min - Giá trị tối thiểu
 * @param {number} max - Giá trị tối đa
 * @returns {number} - Số ngẫu nhiên
 */
function randomInt(min, max) {
  return Math.floor(Math.random() * (max - min + 1)) + min;
}

/**
 * Format thời gian thành string dễ đọc
 * @param {Date} date - Date object
 * @returns {string} - Formatted time string
 */
function formatTime(date = new Date()) {
  return date.toLocaleString('vi-VN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  });
}

/**
 * Kiểm tra xem có phải là URL TikTok hợp lệ không
 * @param {string} url - URL cần kiểm tra
 * @returns {boolean} - True nếu là URL TikTok hợp lệ
 */
function isValidTikTokURL(url) {
  try {
    const urlObj = new URL(url);
    return urlObj.hostname.includes('tiktok.com');
  } catch {
    return false;
  }
}

module.exports = {
  readTextFile,
  readAccountsFile,
  readProxiesFile,
  readCommentsFile,
  sleep,
  generateUUID,
  getRandomComment,
  randomInt,
  formatTime,
  isValidTikTokURL
};
