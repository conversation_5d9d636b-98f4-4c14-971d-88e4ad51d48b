const { chromium } = require('playwright');
const path = require('path');
const { sleep, formatTime } = require('../utils');

class TikTokLoginAutomation {
  constructor(wsServer, dbManager) {
    this.wsServer = wsServer;
    this.dbManager = dbManager;
    this.browsers = new Map(); // accountId -> browser instance
  }

  /**
   * Đăng nhập tài khoản TikTok
   */
  async loginAccount(accountId) {
    try {
      const account = await this.dbManager.getAccountById(accountId);
      if (!account) {
        throw new Error(`Account ${accountId} not found`);
      }

      this.wsServer.sendLog('info', `Starting login for ${account.username}`, accountId);
      await this.dbManager.updateAccount(accountId, { status: 'logging_in' });
      this.wsServer.sendAccountStatusUpdate(accountId, 'logging_in');

      // Tạo profile directory cho account
      const profileDir = path.join(__dirname, '../../profiles', accountId);

      // Khởi tạo browser với profile
      const browser = await chromium.launchPersistentContext(profileDir, {
        headless: false, // Hiển thị browser để user có thể giải CAPTCHA
        viewport: { width: 1280, height: 720 },
        userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        proxy: account.proxy ? {
          server: `http://${account.proxy.host}:${account.proxy.port}`,
          username: account.proxy.username,
          password: account.proxy.password
        } : undefined
      });

      this.browsers.set(accountId, browser);
      const page = await browser.newPage();

      // Điều hướng đến trang đăng nhập TikTok
      this.wsServer.sendLog('info', `Navigating to TikTok login page`, accountId);
      await page.goto('https://www.tiktok.com/login/phone-or-email/email', {
        waitUntil: 'networkidle'
      });

      await sleep(2000, 4000);

      // Kiểm tra xem đã đăng nhập chưa
      const isLoggedIn = await this.checkIfLoggedIn(page);
      if (isLoggedIn) {
        this.wsServer.sendLog('success', `Already logged in for ${account.username}`, accountId);
        await this.dbManager.updateAccount(accountId, { status: 'ready' });
        this.wsServer.sendAccountStatusUpdate(accountId, 'ready');
        return true;
      }

      // Điền thông tin đăng nhập
      this.wsServer.sendLog('info', `Filling login credentials`, accountId);

      // Tìm và điền email/username
      const emailInput = await page.locator('input[name="username"]').first();
      await emailInput.fill(account.username);
      await sleep(1000, 2000);

      // Tìm và điền password
      const passwordInput = await page.locator('input[type="password"]').first();
      await passwordInput.fill(account.password);
      await sleep(1000, 2000);

      // Click nút đăng nhập
      const loginButton = await page.locator('button[data-e2e="login-button"]').first();
      await loginButton.click();

      this.wsServer.sendLog('info', `Login form submitted, waiting for response`, accountId);
      await sleep(3000, 5000);

      // Kiểm tra CAPTCHA hoặc lỗi đăng nhập
      const needsCaptcha = await this.checkForCaptcha(page);
      if (needsCaptcha) {
        this.wsServer.sendLog('warning', `CAPTCHA detected for ${account.username}. Please solve manually.`, accountId);
        await this.dbManager.updateAccount(accountId, { status: 'need_captcha' });
        this.wsServer.sendAccountStatusUpdate(accountId, 'need_captcha');

        // Chờ user giải CAPTCHA (tối đa 5 phút)
        const captchaSolved = await this.waitForCaptchaSolution(page, accountId, 300000);
        if (!captchaSolved) {
          throw new Error('CAPTCHA not solved within timeout');
        }
      }

      // Kiểm tra đăng nhập thành công
      const loginSuccess = await this.waitForLoginSuccess(page, accountId, 30000);
      if (loginSuccess) {
        this.wsServer.sendLog('success', `Login successful for ${account.username}`, accountId);
        await this.dbManager.updateAccount(accountId, {
          status: 'ready',
          stats: {
            ...account.stats,
            lastActivity: new Date().toISOString()
          }
        });
        this.wsServer.sendAccountStatusUpdate(accountId, 'ready');
        return true;
      } else {
        throw new Error('Login failed - unknown error');
      }

    } catch (error) {
      this.wsServer.sendLog('error', `Login failed for account ${accountId}: ${error.message}`, accountId);
      await this.dbManager.updateAccount(accountId, { status: 'error' });
      this.wsServer.sendAccountStatusUpdate(accountId, 'error');
      return false;
    }
  }

  /**
   * Kiểm tra xem đã đăng nhập chưa
   */
  async checkIfLoggedIn(page) {
    try {
      // Kiểm tra các element chỉ xuất hiện khi đã đăng nhập
      const profileButton = page.locator('[data-e2e="profile-icon"]');
      const uploadButton = page.locator('[data-e2e="upload-icon"]');

      const isLoggedIn = await Promise.race([
        profileButton.isVisible().then(visible => visible),
        uploadButton.isVisible().then(visible => visible),
        sleep(5000).then(() => false)
      ]);

      return isLoggedIn;
    } catch (error) {
      return false;
    }
  }

  /**
   * Kiểm tra CAPTCHA
   */
  async checkForCaptcha(page) {
    try {
      const captchaSelectors = [
        '.captcha_verify_container',
        '[data-testid="captcha"]',
        '.verify-slider-track',
        '.secsdk-captcha-wrapper'
      ];

      for (const selector of captchaSelectors) {
        const element = page.locator(selector);
        if (await element.isVisible()) {
          return true;
        }
      }
      return false;
    } catch (error) {
      return false;
    }
  }

  /**
   * Chờ user giải CAPTCHA
   */
  async waitForCaptchaSolution(page, accountId, timeout = 300000) {
    const startTime = Date.now();

    while (Date.now() - startTime < timeout) {
      const hasCaptcha = await this.checkForCaptcha(page);
      if (!hasCaptcha) {
        this.wsServer.sendLog('success', `CAPTCHA solved for account`, accountId);
        return true;
      }

      await sleep(2000);
    }

    return false;
  }

  /**
   * Chờ đăng nhập thành công
   */
  async waitForLoginSuccess(page, accountId, timeout = 30000) {
    const startTime = Date.now();

    while (Date.now() - startTime < timeout) {
      const isLoggedIn = await this.checkIfLoggedIn(page);
      if (isLoggedIn) {
        return true;
      }

      // Kiểm tra lỗi đăng nhập
      const hasError = await this.checkForLoginError(page);
      if (hasError) {
        throw new Error('Invalid credentials or account locked');
      }

      await sleep(2000);
    }

    return false;
  }

  /**
   * Kiểm tra lỗi đăng nhập
   */
  async checkForLoginError(page) {
    try {
      const errorSelectors = [
        '.TUXTextError',
        '[data-e2e="login-error"]',
        '.error-message'
      ];

      for (const selector of errorSelectors) {
        const element = page.locator(selector);
        if (await element.isVisible()) {
          return true;
        }
      }
      return false;
    } catch (error) {
      return false;
    }
  }

  /**
   * Đóng browser cho account
   */
  async closeBrowser(accountId) {
    const browser = this.browsers.get(accountId);
    if (browser) {
      await browser.close();
      this.browsers.delete(accountId);
    }
  }

  /**
   * Lấy browser instance cho account
   */
  getBrowser(accountId) {
    return this.browsers.get(accountId);
  }

  /**
   * Đóng tất cả browsers
   */
  async closeAllBrowsers() {
    for (const [accountId, browser] of this.browsers) {
      try {
        await browser.close();
      } catch (error) {
        console.error(`Error closing browser for ${accountId}:`, error);
      }
    }
    this.browsers.clear();
  }
}

module.exports = TikTokLoginAutomation;
