const { chromium } = require('playwright');
const path = require('path');
const { sleep, randomInt, getRandomComment, formatTime, isValidTikTokURL } = require('../utils');

class FollowInteractAutomation {
  constructor(wsServer, dbManager) {
    this.wsServer = wsServer;
    this.dbManager = dbManager;
    this.activeSessions = new Map(); // accountId -> session info
    this.isRunning = false;
    this.rateLimiter = new Map(); // accountId -> rate limit info
  }

  /**
   * Bắt đầu automation cho nhiều tài khoản
   */
  async startAutomation(accountIds, targetProfile) {
    try {
      if (!isValidTikTokURL(targetProfile)) {
        throw new Error('Invalid TikTok profile URL');
      }

      this.isRunning = true;
      this.wsServer.sendLog('info', `Starting automation for ${accountIds.length} accounts`);

      // Khởi tạo rate limiter cho các tài khoản
      for (const accountId of accountIds) {
        this.initializeRateLimiter(accountId);
      }

      // Chạy automation song song với giới hạn concurrency
      const maxConcurrent = Math.min(3, accountIds.length); // Tối đa 3 tài khoản cùng lúc
      const chunks = this.chunkArray(accountIds, maxConcurrent);

      for (const chunk of chunks) {
        if (!this.isRunning) break;

        const promises = chunk.map(accountId =>
          this.runAccountAutomation(accountId, targetProfile)
            .catch(error => {
              this.wsServer.sendLog('error', `Automation failed for ${accountId}: ${error.message}`, accountId);
            })
        );

        await Promise.all(promises);

        // Nghỉ giữa các batch
        if (chunks.indexOf(chunk) < chunks.length - 1) {
          await sleep(5000, 10000);
        }
      }

      this.wsServer.sendLog('success', 'Automation completed for all accounts');
    } catch (error) {
      this.wsServer.sendLog('error', `Automation error: ${error.message}`);
      throw error;
    }
  }

  /**
   * Chạy automation cho một tài khoản
   */
  async runAccountAutomation(accountId, targetProfile) {
    try {
      const account = await this.dbManager.getAccountById(accountId);
      if (!account) {
        throw new Error(`Account ${accountId} not found`);
      }

      if (account.status !== 'ready') {
        throw new Error(`Account ${account.username} is not ready (status: ${account.status})`);
      }

      // Kiểm tra rate limit
      if (!this.checkRateLimit(accountId)) {
        this.wsServer.sendLog('warning', `Rate limit reached for ${account.username}`, accountId);
        await this.dbManager.updateAccount(accountId, { status: 'limit_reached' });
        this.wsServer.sendAccountStatusUpdate(accountId, 'limit_reached');
        return;
      }

      this.wsServer.sendLog('info', `Starting automation for ${account.username}`, accountId);
      await this.dbManager.updateAccount(accountId, { status: 'running_interact' });
      this.wsServer.sendAccountStatusUpdate(accountId, 'running_interact');

      // Khởi tạo browser session
      const browser = await this.initializeBrowser(account);
      this.activeSessions.set(accountId, { browser, startTime: Date.now() });

      try {
        // Lấy danh sách followers của target
        const followers = await this.scrapeFollowers(browser, targetProfile, accountId);

        if (followers.length === 0) {
          throw new Error('No followers found or unable to access followers list');
        }

        this.wsServer.sendLog('info', `Found ${followers.length} followers to interact with`, accountId);

        // Tương tác với từng follower
        const settings = await this.dbManager.getSettings();
        const maxFollows = Math.min(settings.maxFollowsPerSession || 10, followers.length);

        for (let i = 0; i < maxFollows && this.isRunning; i++) {
          const follower = followers[i];

          try {
            await this.interactWithUser(browser, follower, accountId, settings);

            // Cập nhật stats
            await this.updateAccountStats(accountId);

            // Delay giữa các tương tác
            const delay = randomInt(30000, 60000); // 30-60 giây
            this.wsServer.sendLog('info', `Waiting ${Math.round(delay/1000)}s before next interaction`, accountId);
            await sleep(delay);

          } catch (error) {
            this.wsServer.sendLog('error', `Failed to interact with ${follower.username}: ${error.message}`, accountId);
            continue;
          }
        }

        // Hoàn thành
        await this.dbManager.updateAccount(accountId, { status: 'ready' });
        this.wsServer.sendAccountStatusUpdate(accountId, 'ready');
        this.wsServer.sendLog('success', `Automation completed for ${account.username}`, accountId);

      } finally {
        // Cleanup browser
        await this.closeBrowserSession(accountId);
      }

    } catch (error) {
      await this.dbManager.updateAccount(accountId, { status: 'error' });
      this.wsServer.sendAccountStatusUpdate(accountId, 'error');
      throw error;
    }
  }

  /**
   * Khởi tạo browser với tối ưu performance
   */
  async initializeBrowser(account) {
    const profileDir = path.join(__dirname, '../../profiles', account.id);

    const browser = await chromium.launchPersistentContext(profileDir, {
      headless: true, // Chạy headless để tối ưu performance
      viewport: { width: 1280, height: 720 },
      userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
      proxy: account.proxy ? {
        server: `http://${account.proxy.host}:${account.proxy.port}`,
        username: account.proxy.username,
        password: account.proxy.password
      } : undefined,
      // Performance optimizations
      args: [
        '--no-sandbox',
        '--disable-setuid-sandbox',
        '--disable-dev-shm-usage',
        '--disable-accelerated-2d-canvas',
        '--no-first-run',
        '--no-zygote',
        '--disable-gpu',
        '--disable-background-timer-throttling',
        '--disable-backgrounding-occluded-windows',
        '--disable-renderer-backgrounding'
      ]
    });

    // Block unnecessary resources để tăng tốc độ
    const page = await browser.newPage();
    await page.route('**/*', (route) => {
      const resourceType = route.request().resourceType();
      if (['image', 'stylesheet', 'font', 'media'].includes(resourceType)) {
        route.abort();
      } else {
        route.continue();
      }
    });

    return browser;
  }

  /**
   * Scrape danh sách followers với pagination
   */
  async scrapeFollowers(browser, targetProfile, accountId, maxFollowers = 100) {
    const page = await browser.newPage();
    const followers = [];

    try {
      this.wsServer.sendLog('info', `Navigating to ${targetProfile}`, accountId);
      await page.goto(targetProfile, { waitUntil: 'networkidle', timeout: 30000 });
      await sleep(2000, 4000);

      // Tìm và click vào followers count
      const followersButton = page.locator('[data-e2e="followers-count"]').first();
      if (!(await followersButton.isVisible())) {
        throw new Error('Cannot find followers button - profile may be private');
      }

      await followersButton.click();
      await sleep(3000, 5000);

      this.wsServer.sendLog('info', 'Scraping followers list...', accountId);

      // Scroll và collect followers
      let lastCount = 0;
      let stableCount = 0;

      while (followers.length < maxFollowers && stableCount < 3) {
        // Lấy followers hiện tại trên trang
        const followerElements = await page.locator('[data-e2e="user-item"]').all();

        for (const element of followerElements) {
          if (followers.length >= maxFollowers) break;

          try {
            const usernameElement = element.locator('[data-e2e="user-link"]');
            const username = await usernameElement.getAttribute('href');

            if (username && !followers.find(f => f.username === username)) {
              followers.push({
                username: username.replace('/@', ''),
                profileUrl: `https://www.tiktok.com${username}`
              });
            }
          } catch (error) {
            // Skip invalid elements
            continue;
          }
        }

        // Scroll để load thêm
        await page.evaluate(() => {
          window.scrollTo(0, document.body.scrollHeight);
        });
        await sleep(2000, 3000);

        // Kiểm tra xem có load thêm được không
        if (followers.length === lastCount) {
          stableCount++;
        } else {
          stableCount = 0;
          lastCount = followers.length;
        }

        this.wsServer.sendLog('info', `Collected ${followers.length} followers...`, accountId);
      }

      return followers.slice(0, maxFollowers);

    } catch (error) {
      this.wsServer.sendLog('error', `Failed to scrape followers: ${error.message}`, accountId);
      return [];
    } finally {
      await page.close();
    }
  }

  /**
   * Tương tác với một user (xem video, comment, follow)
   */
  async interactWithUser(browser, follower, accountId, settings) {
    const page = await browser.newPage();

    try {
      this.wsServer.sendLog('info', `Interacting with ${follower.username}`, accountId);

      // Truy cập profile của user
      await page.goto(follower.profileUrl, { waitUntil: 'networkidle', timeout: 30000 });
      await sleep(2000, 4000);

      // Kiểm tra xem đã follow chưa
      const isAlreadyFollowing = await this.checkIfFollowing(page);
      if (isAlreadyFollowing) {
        this.wsServer.sendLog('info', `Already following ${follower.username}, skipping`, accountId);
        return;
      }

      // Lấy danh sách videos
      const videos = await this.getProfileVideos(page, settings.videosToWatch || 3);

      if (videos.length === 0) {
        this.wsServer.sendLog('warning', `No videos found for ${follower.username}`, accountId);
        return;
      }

      // Xem và tương tác với videos
      for (let i = 0; i < Math.min(videos.length, settings.videosToWatch || 3); i++) {
        await this.watchAndInteractWithVideo(page, videos[i], accountId, settings);

        if (i < videos.length - 1) {
          await sleep(5000, 10000); // Delay giữa các video
        }
      }

      // Follow user
      await this.followUser(page, follower.username, accountId);

    } catch (error) {
      this.wsServer.sendLog('error', `Error interacting with ${follower.username}: ${error.message}`, accountId);
      throw error;
    } finally {
      await page.close();
    }
  }

  /**
   * Kiểm tra xem đã follow user chưa
   */
  async checkIfFollowing(page) {
    try {
      const followButton = page.locator('[data-e2e="follow-button"]').first();
      const isVisible = await followButton.isVisible();
      return !isVisible; // Nếu không thấy nút follow thì đã follow rồi
    } catch (error) {
      return false;
    }
  }

  /**
   * Lấy danh sách videos từ profile
   */
  async getProfileVideos(page, maxVideos = 3) {
    try {
      const videos = [];
      const videoElements = await page.locator('[data-e2e="user-post-item"]').all();

      for (let i = 0; i < Math.min(videoElements.length, maxVideos); i++) {
        const videoElement = videoElements[i];
        const videoLink = await videoElement.locator('a').first().getAttribute('href');

        if (videoLink) {
          videos.push({
            url: `https://www.tiktok.com${videoLink}`,
            element: videoElement
          });
        }
      }

      return videos;
    } catch (error) {
      return [];
    }
  }

  /**
   * Xem và tương tác với video
   */
  async watchAndInteractWithVideo(page, video, accountId, settings) {
    try {
      this.wsServer.sendLog('info', `Watching video: ${video.url}`, accountId);

      // Mở video
      await page.goto(video.url, { waitUntil: 'networkidle', timeout: 30000 });
      await sleep(2000, 4000);

      // Xem video trong thời gian ngẫu nhiên
      const watchTime = randomInt(
        Math.max(10000, (settings.watchTimeSeconds || 30) * 1000 * 0.5),
        (settings.watchTimeSeconds || 30) * 1000 * 1.5
      );

      this.wsServer.sendLog('info', `Watching for ${Math.round(watchTime/1000)}s`, accountId);

      // Simulate watching behavior
      await this.simulateWatching(page, watchTime);

      // Random chance to like video (30%)
      if (Math.random() < 0.3) {
        await this.likeVideo(page, accountId);
      }

      // Random chance to comment (20%)
      if (Math.random() < 0.2) {
        await this.commentOnVideo(page, accountId);
      }

    } catch (error) {
      this.wsServer.sendLog('error', `Error watching video: ${error.message}`, accountId);
    }
  }

  /**
   * Simulate natural watching behavior
   */
  async simulateWatching(page, duration) {
    const startTime = Date.now();
    const segments = Math.floor(duration / 5000); // Chia thành các đoạn 5 giây

    for (let i = 0; i < segments && Date.now() - startTime < duration; i++) {
      // Random scroll hoặc click để simulate engagement
      if (Math.random() < 0.3) {
        await page.evaluate(() => {
          window.scrollBy(0, Math.random() * 100 - 50);
        });
      }

      await sleep(4000, 6000);
    }
  }

  /**
   * Like video
   */
  async likeVideo(page, accountId) {
    try {
      const likeButton = page.locator('[data-e2e="like-button"]').first();
      if (await likeButton.isVisible()) {
        await likeButton.click();
        await sleep(1000, 2000);
        this.wsServer.sendLog('info', 'Liked video', accountId);
      }
    } catch (error) {
      // Ignore like errors
    }
  }

  /**
   * Comment on video
   */
  async commentOnVideo(page, accountId) {
    try {
      const comments = await this.dbManager.getComments();
      if (comments.length === 0) return;

      const commentText = getRandomComment(comments);

      // Tìm comment input
      const commentInput = page.locator('[data-e2e="comment-input"]').first();
      if (await commentInput.isVisible()) {
        await commentInput.fill(commentText);
        await sleep(1000, 2000);

        // Submit comment
        const submitButton = page.locator('[data-e2e="comment-post"]').first();
        if (await submitButton.isVisible()) {
          await submitButton.click();
          await sleep(2000, 3000);
          this.wsServer.sendLog('info', `Commented: "${commentText}"`, accountId);
        }
      }
    } catch (error) {
      // Ignore comment errors
      this.wsServer.sendLog('warning', `Failed to comment: ${error.message}`, accountId);
    }
  }

  /**
   * Follow user
   */
  async followUser(page, username, accountId) {
    try {
      this.wsServer.sendLog('info', `Following ${username}`, accountId);
      await this.dbManager.updateAccount(accountId, { status: 'running_follow' });
      this.wsServer.sendAccountStatusUpdate(accountId, 'running_follow');

      const followButton = page.locator('[data-e2e="follow-button"]').first();
      if (await followButton.isVisible()) {
        await followButton.click();
        await sleep(2000, 4000);

        // Verify follow success
        const isFollowing = await this.checkIfFollowing(page);
        if (isFollowing) {
          this.wsServer.sendLog('success', `Successfully followed ${username}`, accountId);
          return true;
        } else {
          throw new Error('Follow action may have failed');
        }
      } else {
        throw new Error('Follow button not found');
      }
    } catch (error) {
      this.wsServer.sendLog('error', `Failed to follow ${username}: ${error.message}`, accountId);
      return false;
    }
  }

  /**
   * Rate limiting functions
   */
  initializeRateLimiter(accountId) {
    const now = Date.now();
    this.rateLimiter.set(accountId, {
      dailyCount: 0,
      sessionCount: 0,
      lastReset: now,
      lastAction: now
    });
  }

  checkRateLimit(accountId) {
    const settings = this.dbManager.getSettings();
    const limits = this.rateLimiter.get(accountId);

    if (!limits) return false;

    const now = Date.now();
    const oneDayMs = 24 * 60 * 60 * 1000;

    // Reset daily count if needed
    if (now - limits.lastReset > oneDayMs) {
      limits.dailyCount = 0;
      limits.lastReset = now;
    }

    // Check limits
    if (limits.dailyCount >= (settings.maxFollowsPerDay || 50)) {
      return false;
    }

    if (limits.sessionCount >= (settings.maxFollowsPerSession || 10)) {
      return false;
    }

    return true;
  }

  async updateAccountStats(accountId) {
    const limits = this.rateLimiter.get(accountId);
    if (limits) {
      limits.dailyCount++;
      limits.sessionCount++;
      limits.lastAction = Date.now();
    }

    // Update database
    const account = await this.dbManager.getAccountById(accountId);
    if (account) {
      await this.dbManager.updateAccount(accountId, {
        stats: {
          ...account.stats,
          followsToday: limits.dailyCount,
          followsThisSession: limits.sessionCount,
          lastActivity: new Date().toISOString()
        }
      });
    }
  }

  /**
   * Utility functions
   */
  chunkArray(array, chunkSize) {
    const chunks = [];
    for (let i = 0; i < array.length; i += chunkSize) {
      chunks.push(array.slice(i, i + chunkSize));
    }
    return chunks;
  }

  async closeBrowserSession(accountId) {
    const session = this.activeSessions.get(accountId);
    if (session) {
      try {
        await session.browser.close();
      } catch (error) {
        console.error(`Error closing browser for ${accountId}:`, error);
      }
      this.activeSessions.delete(accountId);
    }
  }

  /**
   * Stop automation
   */
  async stopAutomation() {
    this.isRunning = false;
    this.wsServer.sendLog('info', 'Stopping automation...');

    // Close all active sessions
    for (const [accountId, session] of this.activeSessions) {
      await this.closeBrowserSession(accountId);
    }

    this.wsServer.sendLog('success', 'Automation stopped');
  }

  /**
   * Get automation status
   */
  getStatus() {
    return {
      isRunning: this.isRunning,
      activeSessions: this.activeSessions.size,
      accounts: Array.from(this.activeSessions.keys())
    };
  }
}

module.exports = FollowInteractAutomation;
